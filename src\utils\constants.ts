// add all env related constants here
export const ENVIRONMENT_VARS = {
  LOG_LEVEL: 'LOG_LEVEL',
  BIG_IQ_USERNAME: 'BIG_IQ_USERNAME',
  BIG_IQ_PASSWORD: 'BIG_IQ_PASSWORD',
  BIG_IQ_LOGIN_PROVIDER_NAME: 'BIG_IQ_LOGIN_PROVIDER_NAME',
  BIGIQ_HOST:'BIGIQ_HOST'
};
export const GENERICHTTPVERB = {
  GET: 'GET',
  POST: 'POST',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};
export const STEPNAME = {
  CHECK_VENAFI_BIGIQ_CONNECTION: 'CHECK_VENAFI_BIGIQ_CONNECTION',
  GENERATE_CSR: 'GENERATE_CSR',
  CHECK_CSR: 'CHECK_CSR',
  SEND_CSR_TO_VENAFI: 'SEND_CSR_TO_VENAFI',
  CHECK_CSR_REQUEST_VENAFI: 'CHECK_CSR_REQUEST_VENAFI',
  RETRIVE_TARGET_BIGIP_MACHINEID_CLUSTERNAME:
    'RETRIVE_TARGET_BIGIP_MACHINEID_CLUSTERNAME',
  RETRIVE_PEER_DEVICE_MACHINEID: 'RETRIVE_PEER_DEVICE_MACHINEID',
  RETRIVE_SSL_CERT: 'RETRIVE_SSL_CERT',
  RETRIVE_SSL_KEY: 'RETRIVE_SSL_KEY',
  RETRIVE_CA_CHAIN: 'RETRIVE_CA_CHAIN',
  RETRIVE_ADCCORE_SELF_LINK: 'RETRIVE_ADCCORE_SELF_LINK',
  CREATE_IPV4_IPV6_NODE: 'CREATE_IPV4_IPV6_NODE',
  CREATE_MONITOR: 'CREATE_MONITOR',
  CREATE_IPV4_IPV6_POOL: 'CREATE_IPV4_IPV6_POOL',
  ADD_IPV4_IPV6_MEMEBER_POOL: 'ADD_IPV4_IPV6_MEMEBER_POOL',
  CREATE_SSL_PROFILE: 'CREATE_SSL_PROFILE',
  RETRIVE_VLAN: 'RETRIVE_VLAN',
  RETRIVE_SNAT: 'RETRIVE_SNAT',
  RETRIVE_TCP: 'RETRIVE_TCP',
  RETRIVE_HTTP: 'RETRIVE_HTTP',
  RETRIVE_HTTP_ANALYTICS: 'RETRIVE_HTTP_ANALYTICS',
  CREATE_IPV4_IPV6_VIP: 'CREATE_IPV4_IPV6_VIP',
  ATTACH_PROFILE: 'ATTACH_PROFILE',
  RETRIVE_IPV4_IPV6_VIP: 'RETRIVE_IPV4_IPV6_VIP',
  RETRIVE_CLUSTER: 'RETRIVE_CLUSTER',
  RETRIVE_CLUSTER_SELF_LINK: 'RETRIVE_CLUSTER_SELF_LINK',
  CREATE_EVALUATION: 'CREATE_EVALUATION',
  CHECK_EVALUATION: 'CHECK_EVALUATION',
  DEPLOY_CONFIGURATION: 'DEPLOY_CONFIGURATION',
  CREATE_VIRTUAL_ADDRESS: 'CREATE_VIRTUAL_ADDRESS',
  EXECUTE_VIRTUAL_ADDRESS: 'EXECUTE_VIRTUAL_ADDRESS',
  CHECK_VIRTUAL_ADDRESS_EXECUTION: 'CHECK_VIRTUAL_ADDRESS_EXECUTION',
  RETRIVE_DNS_SYNC_GROUP: 'RETRIVE_DNS_SYNC_GROUP',
  RETRIVE_DATACENTER: 'RETRIVE_DATACENTER',
  RETRIVE_SERVERS: 'RETRIVE_SERVERS',
  RETRIVE_LTM_SERVER: 'RETRIVE_LTM_SERVER',
  RETRIVE_LTM_VIP_ADDRESS: 'RETRIVE_LTM_VIP_ADDRESS',
  RETRIVE_SYNC_GROUP_PROPERTIES: 'RETRIVE_SYNC_GROUP_PROPERTIES',
  CREATE_GSLB_IPV4_POOL: 'CREATE_GSLB_IPV4_POOL',
  CREATE_GSLB_IPV6_POOL: 'CREATE_GSLB_IPV6_POOL',
  ADD_GSLB_IPV4_POOL_MEMBER: 'ADD_GSLB_IPV4_POOL_MEMBER',
  ADD_GSLB_IPV6_POOL_MEMBER: 'ADD_GSLB_IPV6_POOL_MEMBER',
  CREATE_IPV4_WIDE_IP: 'CREATE_IPV4_WIDE_IP',
  CREATE_IPV6_WIDE_IP: 'CREATE_IPV6_WIDE_IP',
  CREATE_GSLB_EVALUATION: 'CREATE_GSLB_EVALUATION',
  CHECK_GSLB_EVALUATION: 'CHECK_GSLB_EVALUATION',
  CHECK_DEPLOYEMENT: 'CHECK_DEPLOYEMENT',
  DELETE_SCRIPT: 'DELETE_SCRIPT',
  CHECK_GSLB_DEPLOYMENT: 'CHECK_GSLB_DEPLOYMENT',
  DEPLOY_GSLB_CONFIGURATION: 'DEPLOY_GSLB_CONFIGURATION',
};
export const STEPNAMEURL = {
  AUTH: '/mgmt/shared/authn/login',
  CHECK_VENAFI_BIGIQ_CONNECTION:
    'mgmt/cm/adc-core/external-ca/config?$filter= name eq',
  GENERATE_CSR: 'mgmt/cm/adc-core/tasks/certificate-management',
  CHECK_CSR: 'mgmt/cm/adc-core/tasks/certificate-management/',
  SEND_CSR_TO_VENAFI: 'mgmt/cm/adc-core/external-ca/venafi/csr-request',
  CHECK_CSR_REQUEST_VENAFI: 'mgmt/cm/adc-core/external-ca/venafi/csr-request',
  RETRIVE_TARGET_BIGIP_MACHINEID_CLUSTERNAME:
    'mgmt/shared/resolver/device-groups/cm-bigip-allBigIpDevices/devices?$filter=managementAddress eq',
  RETRIVE_PEER_DEVICE_MACHINEID:
    'mgmt/shared/resolver/device-groups/cm-bigip-allBigIpDevices/devices?$filter',
  RETRIVE_SSL_CERT:
    'mgmt/cm/adc-core/working-config/sys/file/ssl-cert?$filter=name eq',
  RETRIVE_SSL_KEY:
    'mgmt/cm/adc-core/working-config/sys/file/ssl-key?$filter=name eq ',
  RETRIVE_CA_CHAIN:
    'mgmt/cm/adc-core/working-config/sys/file/ssl-cert?$filter=name eq',
  RETRIVE_ADCCORE_SELF_LINK:
    'mgmt/shared/resolver/device-groups/cm-adccore-allbigipDevices/devices?$filter=managementAddress eq',
  CREATE_IPV4_IPV6_NODE: 'mgmt/cm/adc-core/working-config/ltm/node/',
  CREATE_MONITOR: 'mgmt/cm/adc-core/working-config/ltm/monitor/http/',
  CREATE_IPV4_IPV6_POOL: 'mgmt/cm/adc-core/working-config/ltm/pool/',
  ADD_IPV4_IPV6_MEMEBER_POOL: 'mgmt/cm/adc-core/working-config/ltm/pool',
  CREATE_SSL_PROFILE: 'mgmt/cm/adc-core/working-config/ltm/profile/client-ssl/',
  RETRIVE_VLAN: 'mgmt/cm/adc-core/working-config/net/vlan',
  RETRIVE_SNAT: 'mgmt/cm/adc-core/working-config/ltm/snatpool?$filter=name eq ',
  RETRIVE_TCP:
    'mgmt/cm/adc-core/working-config/ltm/profile/tcp?$filter=name eq ',
  RETRIVE_HTTP:
    'mgmt/cm/adc-core/working-config/ltm/profile/http?$filter=name eq ',
  RETRIVE_HTTP_ANALYTICS:
    'mgmt/cm/adc-core/working-config/ltm/profile/analytics-http?$filter=name eq',
  CREATE_IPV4_IPV6_VIP: 'mgmt/cm/adc-core/working-config/ltm/virtual',
  ATTACH_PROFILE: 'mgmt/cm/adc-core/working-config/ltm/virtual',
  RETRIVE_IPV4_IPV6_VIP:
    'mgmt/cm/adc-core/working-config/ltm/virtual?$filter=name eq',
  RETRIVE_CLUSTER:
    'mgmt/shared/resolver/device-groups/cm-adccore-allbigipDevices/devices?$filter=managementAddress eq',
  RETRIVE_CLUSTER_SELF_LINK:
    'mgmt/shared/resolver/device-groups/cm-adccore-allbigipDevices/devices',
  DEPLOY_CONFIGURATION: 'mgmt/cm/adc-core/tasks/deploy-configuration',
  CREATE_VIRTUAL_ADDRESS: 'mgmt/shared/user-scripts',
  EXECUTE_VIRTUAL_ADDRESS: 'mgmt/shared/user-script-execution',
  RETRIVE_DNS_SYNC_GROUP:
    'mgmt/cm/dns/current-config/sync-group?$filter=name eq ',
  RETRIVE_DATACENTER: 'mgmt/cm/dns/working-config/datacenter?$filter=name eq ',
  RETRIVE_SERVERS: 'mgmt/cm/dns/working-config/server',
  RETRIVE_LTM_SERVER: 'mgmt/cm/dns/working-config/server?$filter=name eq',
  RETRIVE_SYNC_GROUP_PROPERTIES:
    'mgmt/shared/pipeline/manager/cm-dns-current-config-sync-group',
  CREATE_GSLB_IPV4_POOL: 'mgmt/cm/dns/working-config/pool/a',
  CREATE_GSLB_IPV6_POOL: 'mgmt/cm/dns/working-config/pool/aaaa',
  CREATE_IPV4_WIDE_IP: 'mgmt/cm/dns/working-config/wideip/a',
  CREATE_IPV6_WIDE_IP: 'mgmt/cm/dns/working-config/wideip/aaaa',
  CREATE_GSLB_EVALUATION: 'mgmt/cm/dns/tasks/deploy-configuration',
  CHECK_DEPLOYEMENT: 'mgmt/cm/adc-core/tasks/deploy-configuration',
  DELETE_SCRIPT: 'mgmt/shared/user-scripts',
  CHECk_GSLB_DEPLOYMENT: 'mgmt/cm/dns/tasks/deploy-configuration',
  DEPLOY_GSLB_CONFIGURATION:'mgmt/cm/dns/tasks/deploy-configuration'
};
