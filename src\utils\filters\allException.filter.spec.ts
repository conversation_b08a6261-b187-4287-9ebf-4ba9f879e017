import { HttpAdapterHost } from '@nestjs/core';
import { AllExceptionsFilter } from './allException.filter';
import {
  ArgumentsHost,
  ExceptionFilter,
  HttpStatus,
  InternalServerErrorException,
  BadRequestException,
  HttpException,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { MESSAGES } from '@nestjs/core/constants';
import { LoggerService } from '../../loggers/logger.service';

type MockHttpAdapterHost = Partial<
  Record<keyof HttpAdapterHost, Record<string, jest.Mock>>
>;

describe('AllExceptionsFilter', () => {
  let exceptionFilter: ExceptionFilter;
  let mockHttpAdapterHost: MockHttpAdapterHost;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AllExceptionsFilter,
        {
          provide: LoggerService,
          useValue: { log: jest.fn(), debug: jest.fn(), error: jest.fn() },
        },
        {
          provide: HttpAdapterHost,
          useFactory: () => {
            const mockHttpAdapterHost: MockHttpAdapterHost = {
              httpAdapter: { reply: jest.fn() },
            };
            return mockHttpAdapterHost;
          },
        },
      ],
    }).compile();
    exceptionFilter = module.get<ExceptionFilter>(AllExceptionsFilter);
    mockHttpAdapterHost = module.get<MockHttpAdapterHost>(HttpAdapterHost);
  });

  const internalServerErrorResponseBody = {
    message: 'Nebula service could not complete the operation',
    error: MESSAGES.UNKNOWN_EXCEPTION_MESSAGE,
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
  };

  const mockArgsHost = {
    switchToHttp: () => ({ getResponse: () => {} }),
    getRequest: jest.fn(),
    getResponse: jest.fn(),
  } as unknown as ArgumentsHost;

  it('should reply with correct response body for uncaught errors', () => {
    const err = new Error('Some error');
    mockHttpAdapterHost.httpAdapter.reply.mockImplementationOnce(() => {
      return {};
    });
    exceptionFilter.catch(err, mockArgsHost);
    expect(mockHttpAdapterHost.httpAdapter.reply).toBeCalledTimes(1);
    expect(
      mockHttpAdapterHost.httpAdapter.reply.mock.calls[0][1],
    ).toStrictEqual(internalServerErrorResponseBody);
  });

  it('should reply with correct response body for internal server errors', () => {
    const err = new InternalServerErrorException();
    mockHttpAdapterHost.httpAdapter.reply.mockImplementationOnce(() => {
      return {};
    });
    exceptionFilter.catch(err, mockArgsHost);
    expect(mockHttpAdapterHost.httpAdapter.reply).toBeCalledTimes(1);
    expect(
      mockHttpAdapterHost.httpAdapter.reply.mock.calls[0][1],
    ).toStrictEqual(internalServerErrorResponseBody);
  });

  it('should reply with correct response for http errors other than internal server errors when no message is passed', () => {
    const err = new BadRequestException();
    mockHttpAdapterHost.httpAdapter.reply.mockImplementationOnce(() => {
      return {};
    });
    exceptionFilter.catch(err, mockArgsHost);
    expect(mockHttpAdapterHost.httpAdapter.reply).toBeCalledTimes(1);
    expect(
      mockHttpAdapterHost.httpAdapter.reply.mock.calls[0][1],
    ).toStrictEqual({ message: 'Bad Request', statusCode: 400 });
  });

  it('should reply with correct response body for http errors other than internal server errors when some message is passed', () => {
    const errMsg = 'some error message';
    const err = new BadRequestException(errMsg);
    mockHttpAdapterHost.httpAdapter.reply.mockImplementationOnce(() => {
      return {};
    });
    exceptionFilter.catch(err, mockArgsHost);
    expect(mockHttpAdapterHost.httpAdapter.reply).toBeCalledTimes(1);
    expect(
      mockHttpAdapterHost.httpAdapter.reply.mock.calls[0][1],
    ).toStrictEqual({ error: 'Bad Request', statusCode: 400, message: errMsg });
  });

  it('should reply with correct response body for errors thrown directly using HttPException class ', () => {
    const errMsg = 'some error message';
    const err = new HttpException(errMsg, HttpStatus.CONFLICT);
    mockHttpAdapterHost.httpAdapter.reply.mockImplementationOnce(() => {
      return {};
    });
    exceptionFilter.catch(err, mockArgsHost);
    expect(mockHttpAdapterHost.httpAdapter.reply).toBeCalledTimes(1);
    expect(
      mockHttpAdapterHost.httpAdapter.reply.mock.calls[0][1],
    ).toStrictEqual({ statusCode: HttpStatus.CONFLICT, message: errMsg });
  });
});
