import { Injectable, Inject } from '@nestjs/common';
import { AxiosInstance } from 'axios';
import { LoggerService } from 'src/loggers/logger.service';
import { LoginPayload } from './dto/load-balancer-services.dto';
import {
  MetaDataPolicyFolderDto,
  MetaDataCaConfigResponseDto,
  MetaDataPolicyFolderResponseDto,
} from './dto/meta-data-config.dto';
import { withResponseErrorHandler } from 'src/utils/helpers';
import { ClusterDetailsResponseDto } from './dto/cluster-details.dto';
@Injectable()
export class LoadBalancerServices {
  private token: string | null = null;
  constructor(
    private readonly logger: LoggerService,
    @Inject('AXIOS_INSTANCE') private readonly axiosInstance: AxiosInstance,
  ) {}

  async createToken(): Promise<void> {
    const url: string = '/mgmt/shared/authn/login';
    const headers = {
      'Content-Type': 'text/plain',
    };

    const username: string = process.env.BIG_IQ_USERNAME;
    const password: string = process.env.BIG_IQ_PASSWORD;
    const loginProviderName: string = process.env.BIG_IQ_LOGIN_PROVIDER_NAME;

    const payload: LoginPayload = {
      username,
      password,
      loginProviderName,
    };

    try {
      this.logger.log('create token wrapper is running');
      const response = await withResponseErrorHandler(
        this.axiosInstance.post(url, payload, { headers }),
      );
      this.token = response.token.token;
      return;
    } catch (error) {
      this.logger.error(error, 'error in gettoken wrapper');
    }
  }

  async getMetaDataCaConfig(): Promise<MetaDataCaConfigResponseDto> {
    const url: string = `/mgmt/cm/adc-core/external-ca/config`;
    this.logger.log('get meta data config wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getPolicyFolders(
    policyFolderRequestDetails: MetaDataPolicyFolderDto,
  ): Promise<MetaDataPolicyFolderResponseDto> {
    const url: string = `/mgmt/cm/adc-core/external-ca/venafi/policy-folder`;
    this.logger.log('get policy folder wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.post(url, policyFolderRequestDetails, { headers }),
    );
  }

  async getClusterDetails(): Promise<ClusterDetailsResponseDto> {
    const url: string = `/mgmt/shared/resolver/device-groups/cm-bigip-allBigIpDevices/devices?$filter=managementAddress eq '${process.env.BIG_IP_MGMT_IP}'`;
    this.logger.log('get cluster details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getPeerDeviceDetails(
    clusterName: string,
  ): Promise<ClusterDetailsResponseDto> {
    const url: string = `/mgmt/shared/resolver/device-groups/cm-adccore-allbigipDevices/devices?$filter=address ne '${process.env.BIG_IP_MGMT_IP}' and properties.clusterName eq '${clusterName}'`;
    this.logger.log('get peer device details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getPinningDeviceDetails() {
    const url: string = `/mgmt/cm/adc-core/working-config/root-node`;
    this.logger.log('get pinning device details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getSslCertificate() {
    const url: string = `/mgmt/cm/adc-core/working-config/sys/file/ssl-cert?$filter=name eq '${process.env.BIG_IQ_CN}.crt'`;
    this.logger.log('get ssl certificate details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getSslCertificateKey() {
    const url: string = `/mgmt/cm/adc-core/working-config/sys/file/ssl-key?$filter=name eq '${process.env.BIG_IQ_CN}.key'`;
    this.logger.log('get ssl certificate key wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async addCertificateDetails(certificateDetails) {
    const url: string = `/mgmt/cm/adc-core/tasks/update-root-nodes`;
    this.logger.log('add Certificate Details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.post(url, certificateDetails, { headers }),
    );
  }

  async deployCertificateDetails(certificateDetails) {
    const url: string = `/mgmt/cm/adc-core/tasks/deploy-configuration`;
    this.logger.log('deploy Certificate Details wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.post(url, certificateDetails, { headers }),
    );
  }

  async checkDeploymentStatus(deploymentTaskId) {
    const url: string = `/mgmt/cm/adc-core/tasks/deploy-configuration/${deploymentTaskId}`;
    this.logger.log('check deployment status wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async getAs3Declaration() {
    const url: string = `/mgmt/shared/appsvcs/declare`;
    this.logger.log('get as3 declaration wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }

  async deployVips(payload) {
    const url: string = `/mgmt/cm/global/tasks/deploy-to-application`;
    this.logger.log('deploy vips wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.post(url, payload, { headers }),
    );
  }

  async checkAs3Status(deploymentTaskId) {
    const url: string = `/mgmt/cm/global/tasks/deploy-to-application/${deploymentTaskId}`;
    this.logger.log('check as3 status wrapper is running');

    await this.createToken();

    const headers = {
      'X-F5-Auth-Token': this.token,
      'Content-type': 'application/json',
    };

    return await withResponseErrorHandler(
      this.axiosInstance.get(url, { headers }),
    );
  }
}
