import { IsString, IsObject } from 'class-validator';

export class ClusterDetailsResponseDto {
  totalItems: number;
  items: ClusterItems[];
  generation: string;
  kind: string;
  lastUpdateMicros: number;
  selfLink: string;
}

class ClusterItems {
  lastUpdateMicros: number;
  edition: string;
  uuid: string;
  httpsPort: string;
  hostname: string;
  mcpDeviceName: string;
  isLicenseExpired: boolean;
  restFrameworkVersion: string;
  state: string;
  generation: number;
  managementAddress: string;
  product: string;
  address: string;
  kind: string;
  version: string;
  selfLink: string;
  slots: Slots[];
  groupName: string;
  machineId: string;
  isClustered: boolean;
  build: string;
  platformMarketingName: string;
  deviceUri: string;
  isVirtual: string;
  properties?: Properties;
}

export class Slots {
  volume: string;
  product: string;
  build: string;
  isActive: boolean;
  version: number;
}

export class Properties {
  clusterName: string;
  'shared:resolver:device-groups:discoverer': string;
}

export class ClusterResponseDto {
  bigIpDevices: BigIpDevice[];
  clusterName: string;
}

export class BigIpDevice {
  machineId: BigIpDevice;
  hostname: string;
  managementAddress: string;
  selfLink: boolean;
  rootNode?: string;
}

