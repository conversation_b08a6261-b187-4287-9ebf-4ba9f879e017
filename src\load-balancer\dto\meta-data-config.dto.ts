import { IsString, IsObject } from 'class-validator';

class ExternalCaConfigReference {
  @IsString()
  link: string;
}

export class MetaDataPolicyFolderDto {
  @IsObject({ each: true })
  externalCaConfigReference: ExternalCaConfigReference;

  @IsString()
  policyFolderPath: string;
}

export class MetaDataCaConfigResponseDto {
  generation: number;
  kind: string;
  lastUpdateMicros: number;
  selfLink: string;
  items: CaConfigItems[];
}

export class MetaDataPolicyFolderResponseDto {
  policyFolderPath: string;
  externalCaConfigReference: {
    link: string;
  };
  policyFolders: PolicyFolder[];
}

class CaConfigItems {
  kind: string;
  name: string;
  uuid: string;
  selfLink: string;
  userName: string;
  caProvider: string;
  generation: number;
  webSdkEndPoint: string;
  isTokenBasedAuth: boolean;
  lastUpdateMicros: number;
  encryptedPassword: string;
  autoDeployThreshold: string;
  autoRenewalThreshold: number;
}

class PolicyFolder {
  absoluteGUID: string;
  dn: string;
  guid: string;
  id: string;
  name: string;
  parent: string;
  revision: string;
  typeName: string;
}