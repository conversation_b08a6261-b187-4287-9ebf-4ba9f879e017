import { Module } from '@nestjs/common';
import { LoadBalancerController } from './load-balancer-controller';
import { LoadBalancerService } from './load-balancer-services';
import { LoadBalancerServices } from './load-balancer-wrapper-services';
import { HttpModule } from '@nestjs/axios';
import axios, { AxiosInstance } from 'axios';
import { BigIQWrapperService } from './big-iq-wrapper-service';
@Module({
  imports: [HttpModule],
  controllers: [LoadBalancerController],
  providers: [
    LoadBalancerService,
    LoadBalancerServices,
    {
      provide: 'AXIOS_INSTANCE',
      useFactory: (): AxiosInstance => {
        const axiosInstance = axios.create({
          baseURL: process.env.BIG_IQ_BASE_URL,
        });

        return axiosInstance;
      },
    },
    BigIQWrapperService,
    {
      provide: 'AXIOS_INSTANCE',
      useFactory: (): AxiosInstance => {
        const axiosInstance = axios.create({
          baseURL: process.env.BIG_IQ_BASE_URL,
        });

        return axiosInstance;
      },
    },
  ],
  exports: ['AXIOS_INSTANCE'],
})
export class LoadBalancerModule {}
