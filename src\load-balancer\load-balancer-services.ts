import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { LoggerService } from '../loggers/logger.service';
import { LoadBalancerServices } from './load-balancer-wrapper-services';
import {
  MetaDataPolicyFolderDto,
  MetaDataCaConfigResponseDto,
  MetaDataPolicyFolderResponseDto,
} from './dto/meta-data-config.dto';
import { ClusterResponseDto } from './dto/cluster-details.dto';
import {
  Mocked_MetaData_Config,
  Get_Policy_Folders,
  DEPLOY_VIPS,
  ADD_CERT,
} from './constant';
import { GenericAPIDto } from './dto/generic.api.dto';
import { GENERICHTTPVERB, STEPNAME, STEPNAMEURL } from '../utils/constants';
import { BigIQWrapperService } from './big-iq-wrapper-service';

@Injectable()
export class LoadBalancerService {
  constructor(
    private readonly logger: LoggerService,
    private readonly wrapperService: LoadBalancerServices,
    private readonly bigIQService: BigIQWrapperService,
  ) {}

  async getMetaDataCaConfig(): Promise<MetaDataCaConfigResponseDto> {
    this.logger.log(
      'calling wrapper service to get the ca config meta data details',
    );
    const bigIqHost = process.env.BIGIQ_HOST;
    const venafiProvider = process.env.VENAFI_PROVIDER;
    const url: string = `https://${bigIqHost}/mgmt/cm/adc-core/external-ca/config?$filter=name%20eq%20'${venafiProvider}'`;
    this.logger.log('configUrl', url);
    const response = this.genericGet(url);
    return response;
  }

  async getPolicyFolders(
    policyFolderRequestDetails: MetaDataPolicyFolderDto,
  ): Promise<MetaDataPolicyFolderResponseDto> {
    this.logger.log('calling BIG IQ service to get the policy details');
    const bigIqHost = process.env.BIGIQ_HOST;
    const url =
      policyFolderRequestDetails.externalCaConfigReference.link.replace(
        'localhost',
        bigIqHost,
      );
    const response = this.genericGet(url);
    this.logger.log('Policy folder', JSON.stringify(response));
    return response;
  }

  async getClusterDetails(): Promise<ClusterResponseDto> {
    this.logger.log('calling wrapper service to get the cluster details');
    const response = await this.wrapperService.getClusterDetails();
    this.logger.log('response from getCluster', response);
    let bigIpDevices = [];
    let clusterName;
    if (response.totalItems === 1) {
      let machineId = response.items[0].machineId || 'N/A';
      let hostname = response.items[0].hostname || 'N/A';
      let managementAddress = response.items[0].managementAddress || 'N/A';
      let selfLink = response.items[0].selfLink || 'N/A';
      clusterName = response.items[0].properties?.clusterName || 'None';

      // Add the primary device to the array
      bigIpDevices.push({
        machineId: machineId,
        hostname: hostname,
        managementAddress: managementAddress,
        selfLink: selfLink,
      });

      // log the cluster name to a variable to check for peers
      this.logger.log('TARGET-BIG-IP-CLUSTER-NAME', clusterName);

      // Log the primary device details
      this.logger.log(
        `Primary Device: Machine ID = ${machineId}, Self Link = ${selfLink}, Hostname = ${hostname}, Management Address = ${managementAddress}, Cluster Name = ${clusterName}`,
      );
    } else {
      this.logger.error('Failed to retrieve proper target BIG-IP attributes.');
      throw new InternalServerErrorException(
        'Failed to retrieve proper target BIG-IP attributes.',
      );
    }
    const finalResponse = {
      bigIpDevices: bigIpDevices,
      clusterName: clusterName,
    };
    return finalResponse;
  }

  async getPeerDeviceDetails(data): Promise<ClusterResponseDto> {
    this.logger.log('calling wrapper service to get the peer device details');
    const response = await this.wrapperService.getPeerDeviceDetails(
      data.clusterName,
    );

    // Initialize an array to store extracted data or retrieve an existing one
    let bigIpDevices = data.bigIpDevices;

    // Function to find the index of an existing device
    function findDeviceIndex(machineId) {
      return bigIpDevices.findIndex((device) => device.machineId === machineId);
    }

    // Check if there are items to process
    if (response.totalItems > 0) {
      // Iterate through each item in the response
      response.items.forEach((item) => {
        // Extract the desired attributes
        let machineId = item.machineId || 'N/A';
        let hostname = item.hostname || 'N/A';
        let managementAddress = item.managementAddress || 'N/A';
        let selfLink = item.selfLink || 'N/A';

        // Check if the device already exists
        let existingDeviceIndex = findDeviceIndex(machineId);

        if (existingDeviceIndex !== -1) {
          // Overwrite the existing device
          bigIpDevices[existingDeviceIndex] = {
            machineId: machineId,
            hostname: hostname,
            managementAddress: managementAddress,
            selfLink: selfLink,
          };
          this.logger.log(
            `Overwritten: Machine ID = ${machineId}, Self Link = ${selfLink},Hostname = ${hostname}, Management Address = ${managementAddress}`,
          );
        } else {
          // Add the new device
          bigIpDevices.push({
            machineId: machineId,
            hostname: hostname,
            managementAddress: managementAddress,
            selfLink: selfLink,
          });
          this.logger.log(
            `Added: Machine ID = ${machineId}, Hostname = ${hostname}, Management Address = ${managementAddress}`,
          );
          this.logger.log(`bigIpDevices = ${bigIpDevices}`);
        }
      });

      // Updated bigIpDevices Array
      this.logger.log(`BIG-IP-DEVICES :`, bigIpDevices);
    } else {
      this.logger.error('No BIG-IP devices found..');
      throw new InternalServerErrorException('No BIG-IP devices found.');
    }
    const finalResponse = {
      bigIpDevices: bigIpDevices,
      clusterName: data.clusterName,
    };
    return finalResponse;
  }

  async getPinningDeviceDetails(data): Promise<ClusterResponseDto> {
    this.logger.log(
      'calling wrapper service to get the pinning device details',
    );
    const response = await this.wrapperService.getPinningDeviceDetails();

    // Define the target variable to update
    let bigIpDevices = data.bigIpDevices;

    if (response.items) {
      response.items.forEach((item) => {
        const machineId = item.deviceReference?.machineId;
        const selfLink = item.selfLink;

        // Update the corresponding entry in the BIG_IP_DEVICES array
        bigIpDevices = bigIpDevices.map((device) => {
          if (device.machineId === machineId) {
            return { ...device, rootNode: selfLink };
          }
          return device;
        });
      });

      //  updated bigIpDevices array

      this.logger.log('BIG_IP_DEVICES updated successfully:', bigIpDevices);
    } else {
      this.logger.error('Could not find existing device array.');
      throw new InternalServerErrorException(
        'Could not find existing device array.',
      );
    }

    const finalResponse = {
      bigIpDevices: bigIpDevices,
      clusterName: data.clusterName,
    };
    return finalResponse;
  }

  async getSslCertificate() {
    this.logger.log('calling wrapper service to get ssl certificate details');
    const response = await this.wrapperService.getSslCertificate();
    let finalResponse;
    if (response.totalItems == 1) {
      finalResponse = { certificateSelfLink: response.items['0'].selfLink };
      this.logger.log('Certificate Self-Link:', response.items['0'].selfLink);
    } else {
      this.logger.error('Failed to retrieve certificate self link');
      throw new InternalServerErrorException(
        'Failed to retrieve certificate self link',
      );
    }
    return finalResponse;
  }

  async getSslCertificateKey() {
    this.logger.log(
      'calling wrapper service to get ssl certificate key details',
    );
    const response = await this.wrapperService.getSslCertificateKey();
    let finalResponse;
    if (response.totalItems == 1) {
      finalResponse = { keySelfLink: response.items['0'].selfLink };
      this.logger.log('Key Self-Link:', response.items['0'].selfLink);
    } else {
      this.logger.error('Failed to retrieve key self link');
      throw new InternalServerErrorException(
        'Failed to retrieve key self link',
      );
    }
    return finalResponse;
  }

  async addCertificateDetails(data) {
    this.logger.log('calling wrapper service to add certificate details');

    // Retrieve BIG-IP-DEVICES collection variable
    let bigIpDevices = data.bigIpDevices;

    if (bigIpDevices) {
      // Construct the targetReferences array
      const targetReferences = bigIpDevices
        .map((device) => {
          if (device.rootNode) {
            return {
              link: device.rootNode,
            };
          } else {
            this.logger.log(`Device ${device.name} does not have a selfLink`);
          }
        })
        .filter(Boolean); // Filter out undefined entries

      // Define the payload template
      const payload = {
        name: ADD_CERT.NAME,
        skipCoordination: true,
        properties: [
          {
            name: 'sslKeyReferences',
            action: 'ARRAY_INSERT',
            value: [
              {
                link: data.keySelfLink,
                tags: ['user'],
              },
            ],
          },
          {
            name: 'sslCertReferences',
            action: 'ARRAY_INSERT',
            value: [
              {
                link: data.certificateSelfLink,
                tags: ['user'],
              },
            ],
          },
        ],
        targetReferences: targetReferences,
      };
      this.logger.log('Payload constructed and saved:', payload);
      const response = await this.wrapperService.addCertificateDetails(payload);
      return response;
    } else {
      this.logger.error('BIG-IP-DEVICES not found.');
      throw new InternalServerErrorException(
        'BIG-IP-DEVICES collection not found.',
      );
    }
  }

  async deployCertificateDetails(data) {
    this.logger.log('calling wrapper service to deploy certificate details');
    // Retrieve BIG_IP_DEVICES collection variable
    let bigIpDevices = data.bigIpDevices;

    if (bigIpDevices) {
      // Construct the targetReferences array
      const targetReferences = bigIpDevices
        .map((device) => {
          if (device.selfLink) {
            return {
              link: device.selfLink,
            };
          } else {
            this.logger.log(`Device ${device.name} does not have a selfLink`);
          }
        })
        .filter(Boolean); // Filter out undefined entries

      // Define the payload template
      const payload = {
        skipVerifyConfig: false,
        skipDistribution: false,
        snapshotReference: null,
        objectsToDeployReferences: [
          {
            link: data.keySelfLink,
          },
          {
            link: data.certificateSelfLink,
          },
        ],
        name: 'Nebula Certificate Pinning',
        deploySpecifiedObjectsOnly: false,
        deviceReferences: targetReferences,
      };

      this.logger.log('Payload constructed and saved:', payload);
      const response =
        await this.wrapperService.deployCertificateDetails(payload);
      this.logger.log('response from deployment:', response);
      const finalResponse = { deploymentTaskId: response.id };
      return finalResponse;
    } else {
      this.logger.error('big ip devices not found.');
      throw new InternalServerErrorException('big ip devices not found.');
    }
  }

  async checkDeploymentStatus(deploymentTaskId) {
    this.logger.log('calling wrapper service to check deployment status');
    const response =
      await this.wrapperService.checkDeploymentStatus(deploymentTaskId);
    return response;
  }

  async getAs3Declaration() {
    this.logger.log('calling wrapper service to get as3 declaration');
    const response = await this.wrapperService.getAs3Declaration();
    return response;
  }

  async deployVips(data) {
    this.logger.log('calling wrapper service to deploy vips');
    const payload = await this.preparePayloadToDeployVips(data);
    const response = await this.wrapperService.deployVips(payload);
    return response;
  }

  async preparePayloadToDeployVips(data) {
    this.logger.log('inside preparePayloadToDeployVips deploy vips');
    const payload = {
      $schema: DEPLOY_VIPS.SCHEMA,
      applicationDescription: DEPLOY_VIPS.APPLICATION_DESC,
      applicationName: DEPLOY_VIPS.APPLICATION_NAME,
      appSvcsDeclaration: {
        class: DEPLOY_VIPS.APP_SVS_CLASS,
        declaration: {
          class: DEPLOY_VIPS.DECLARATION_CLASS,
          label: DEPLOY_VIPS.DECLARATION_LABEL,
          remark: DEPLOY_VIPS.DECLARATION_REMARK,
          target: {
            address: process.env.BIG_IP_MGMT_IP,
          },
          schemaVersion: DEPLOY_VIPS.SCHEMA_VERSION,
          id: DEPLOY_VIPS.DECLARATION_ID,
          AS3_CAAS_PARTITION: data.AS3_CAAS_PARTITION,
        },
      },
    };
    return payload;
  }

  async checkAs3Status(deploymentTaskId) {
    this.logger.log('calling wrapper service to check as3 status');
    const response = await this.wrapperService.checkAs3Status(deploymentTaskId);
    return response;
  }
  async generic(data: GenericAPIDto, file?: Express.Multer.File) {
    let uri;
    //Below case will replicate the steps in order to create bigiq request,
    // Some case may need to call multiple time from n8n like ipv4 node,pool, members
    //Some case may need to delay before call next step like certificate generation and check certificate status
    switch (data.stepName) {
      case STEPNAME.CHECK_VENAFI_BIGIQ_CONNECTION:
        uri = `${STEPNAMEURL.CHECK_VENAFI_BIGIQ_CONNECTION} '${data.payload.venafiProviderName}'`;
        return await this.genericGet(uri);

      case STEPNAME.GENERATE_CSR:
        return await this.genericPost(STEPNAMEURL.GENERATE_CSR, data.payload);

      case STEPNAME.CHECK_CSR:
        // set some delay after generate csr and before check, big iq takes sometime to create the certificate
        uri = `${STEPNAMEURL.CHECK_CSR}/${data.payload.csrId}`;
        return await this.genericGet(uri);

      case STEPNAME.SEND_CSR_TO_VENAFI:
        return await this.genericPost(
          STEPNAMEURL.SEND_CSR_TO_VENAFI,
          data.payload,
        );

      case STEPNAME.CHECK_CSR_REQUEST_VENAFI:
        uri = `${STEPNAMEURL.CHECK_CSR_REQUEST_VENAFI}/${data.payload.csrRequestId}`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_TARGET_BIGIP_MACHINEID_CLUSTERNAME:
        uri = `${STEPNAMEURL.RETRIVE_TARGET_BIGIP_MACHINEID_CLUSTERNAME} '${data.payload.bigIpAddress}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_PEER_DEVICE_MACHINEID:
        uri = `${STEPNAMEURL.RETRIVE_PEER_DEVICE_MACHINEID}= address ne '${data.payload.bigIpAddress}' and properties.clusterName eq '${data.payload.clusterName}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_SSL_CERT:
        uri = `${STEPNAMEURL.RETRIVE_SSL_CERT} '${data.payload.certName}.crt'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_SSL_KEY:
        uri = `${STEPNAMEURL.RETRIVE_SSL_KEY} '${data.payload.certName}.key'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_CA_CHAIN:
        uri = `${STEPNAMEURL.RETRIVE_CA_CHAIN} '${data.payload.certCARootName}.crt'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_ADCCORE_SELF_LINK:
        uri = `${STEPNAMEURL.RETRIVE_ADCCORE_SELF_LINK} '${data.payload.bigIpAddress}'`;
        return await this.genericGet(uri);

      //Below step can execute multiple times for each ip address
      case STEPNAME.CREATE_IPV4_IPV6_NODE:
        return await this.genericPost(
          STEPNAMEURL.CREATE_IPV4_IPV6_NODE,
          data.payload,
        );

      case STEPNAME.CREATE_MONITOR:
        return await this.genericPost(STEPNAMEURL.CREATE_MONITOR, data.payload);

      case STEPNAME.CREATE_IPV4_IPV6_POOL:
        return await this.genericPost(
          STEPNAMEURL.CREATE_IPV4_IPV6_POOL,
          data.payload,
        );

      case STEPNAME.ADD_IPV4_IPV6_MEMEBER_POOL:
        uri = `${STEPNAMEURL.ADD_IPV4_IPV6_MEMEBER_POOL}/${data.payload.poolId}/members`;
        return await this.genericPost(uri, data.payload.members);

      case STEPNAME.CREATE_SSL_PROFILE:
        return await this.genericPost(
          STEPNAMEURL.CREATE_SSL_PROFILE,
          data.payload,
        );

      case STEPNAME.RETRIVE_VLAN:
        return await this.genericGet(STEPNAMEURL.RETRIVE_VLAN);

      case STEPNAME.RETRIVE_SNAT:
        uri = `${STEPNAMEURL.RETRIVE_SNAT} '${data.payload.snatName}'`; //SNAT CAN READ FROM ENV CONFIG
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_TCP:
        uri = `${STEPNAMEURL.RETRIVE_TCP} 'tcp'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_HTTP:
        uri = `${STEPNAMEURL.RETRIVE_HTTP} 'http'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_HTTP_ANALYTICS:
        uri = `${STEPNAMEURL.RETRIVE_HTTP_ANALYTICS} 'CUSTOM_HTTP_S_ANALYTICS'`;
        return await this.genericGet(uri);

      case STEPNAME.CREATE_IPV4_IPV6_VIP:
        return await this.genericPost(
          STEPNAMEURL.CREATE_IPV4_IPV6_VIP,
          data.payload,
        );

      case STEPNAME.ATTACH_PROFILE:
        uri = `${STEPNAMEURL.ATTACH_PROFILE}/${data.payload.virtualServerId}/profiles`;
        return await this.genericPost(uri, data.payload.profiles);

      case STEPNAME.RETRIVE_IPV4_IPV6_VIP:
        uri = `${STEPNAMEURL.RETRIVE_IPV4_IPV6_VIP} '${data.payload.virtualServerName}'&$select=selfLink`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_CLUSTER:
        uri = `${STEPNAMEURL.RETRIVE_CLUSTER} '${data.payload.bigIp}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_CLUSTER_SELF_LINK:
        uri = `${STEPNAMEURL.RETRIVE_CLUSTER_SELF_LINK}`;
        return await this.genericGet(uri);

      case STEPNAME.CREATE_EVALUATION:
        return await this.genericPost(
          STEPNAMEURL.DEPLOY_CONFIGURATION,
          data.payload,
        );

      case STEPNAME.CHECK_EVALUATION:
        uri = `${STEPNAMEURL.DEPLOY_CONFIGURATION}/${data.payload.evaluationId}`;
        return await this.genericGet(uri);

      case STEPNAME.DEPLOY_CONFIGURATION:
        uri = `${STEPNAMEURL.DEPLOY_CONFIGURATION}/${data.payload.id}`;
        return await this.genericPatch(uri, data.payload);

      case STEPNAME.CHECK_DEPLOYEMENT:
        uri = `${STEPNAMEURL.CHECK_DEPLOYEMENT}/${data.payload.deploymentId}`;
        return await this.genericGet(uri);
      case STEPNAME.CREATE_VIRTUAL_ADDRESS:
        return await this.genericPost(
          STEPNAMEURL.CREATE_VIRTUAL_ADDRESS,
          data.payload,
        );

      case STEPNAME.EXECUTE_VIRTUAL_ADDRESS:
        return await this.genericPost(
          STEPNAMEURL.EXECUTE_VIRTUAL_ADDRESS,
          data.payload,
        );

      case STEPNAME.CHECK_VIRTUAL_ADDRESS_EXECUTION:
        uri = `${STEPNAMEURL.EXECUTE_VIRTUAL_ADDRESS}/${data.payload.scriptTaskId}`;
        return await this.genericGet(uri);

      case STEPNAME.DELETE_SCRIPT:
        uri = `${STEPNAMEURL.DELETE_SCRIPT}/${data.payload.scriptId}`;
        return await this.genericDelete(uri);

      case STEPNAME.RETRIVE_DNS_SYNC_GROUP:
        uri = `${STEPNAMEURL.RETRIVE_DNS_SYNC_GROUP} '${data.payload.syncGroup}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_DATACENTER:
        uri = `${STEPNAMEURL.RETRIVE_DATACENTER} '${data.payload.datacenterName}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_SERVERS:
        uri = `${STEPNAMEURL.RETRIVE_SERVERS}`;
        return await this.genericGet(uri);

      //This case will execute for both LTM A and B servers
      case STEPNAME.RETRIVE_LTM_SERVER:
        uri = `${STEPNAMEURL.RETRIVE_LTM_SERVER} '${data.payload.serverName}'`;
        return await this.genericGet(uri);

      //This case will execute for both LTM A and B servers and IPV4 and IPV6 address
      case STEPNAME.RETRIVE_LTM_VIP_ADDRESS:
        uri = `${STEPNAMEURL.RETRIVE_SERVERS}/${data.payload.ltmId}/virtual-servers?$filter=name eq '${data.payload.virtualServer}'`;
        return await this.genericGet(uri);

      case STEPNAME.RETRIVE_SYNC_GROUP_PROPERTIES:
        uri = `${STEPNAMEURL.RETRIVE_SYNC_GROUP_PROPERTIES}`;
        return await this.genericPost(uri, data.payload);

      case STEPNAME.CREATE_GSLB_IPV4_POOL:
        return await this.genericPost(
          STEPNAMEURL.CREATE_GSLB_IPV4_POOL,
          data.payload,
        );

      case STEPNAME.CREATE_GSLB_IPV6_POOL:
        return await this.genericPost(
          STEPNAMEURL.CREATE_GSLB_IPV6_POOL,
          data.payload,
        );

      //This case will execute for both LTM A and LTM B servers, IPV4
      case STEPNAME.ADD_GSLB_IPV4_POOL_MEMBER:
        uri = `${STEPNAMEURL.CREATE_GSLB_IPV4_POOL}/${data.payload.gslbPoolId}/members`;
        return await this.genericPost(uri, data.payload.members);

      //This case will execute for both LTM A and LTM B servers, IPV6
      case STEPNAME.ADD_GSLB_IPV6_POOL_MEMBER:
        uri = `${STEPNAMEURL.CREATE_GSLB_IPV6_POOL}/${data.payload.gslbPoolId}/members`;
        return await this.genericPost(uri, data.payload.members);

      case STEPNAME.CREATE_IPV4_WIDE_IP:
        uri = `${STEPNAMEURL.CREATE_IPV4_WIDE_IP}`;
        return await this.genericPost(uri, data.payload);

      case STEPNAME.CREATE_IPV6_WIDE_IP:
        uri = `${STEPNAMEURL.CREATE_IPV6_WIDE_IP}`;
        return await this.genericPost(uri, data.payload);

      case STEPNAME.CREATE_GSLB_EVALUATION:
        uri = `${STEPNAMEURL.CREATE_GSLB_EVALUATION}`;
        return await this.genericPost(uri, data.payload);

      case STEPNAME.CHECK_GSLB_EVALUATION:
        uri = `${STEPNAMEURL.CREATE_GSLB_EVALUATION}/${data.payload.id}`;
        return await this.genericPatch(uri, data.payload);

      case STEPNAME.DEPLOY_GSLB_CONFIGURATION:
        uri = `${STEPNAMEURL.DEPLOY_GSLB_CONFIGURATION}/${data.payload.id}`;
        return await this.genericPatch(uri, data.payload);
      case STEPNAME.CHECK_GSLB_DEPLOYMENT:
        uri = `${STEPNAMEURL.CHECk_GSLB_DEPLOYMENT}/${data.payload.deploymentId}`;
        return await this.genericGet(uri);

      default:
        throw new BadRequestException(
          `${data.stepName} step name is not supported in load balancer service`,
        );
    }
  }

  private async genericGet(uri: string) {
    this.logger.log('generic get api call started', uri);
    const result = await this.bigIQService.generic(uri, GENERICHTTPVERB.GET);
    this.logger.log('generic get api call completed', uri);
    return result;
  }
  private async genericPost(uri: string, payload: any) {
    this.logger.log('generic post api call started', uri);
    const result = await this.bigIQService.generic(
      uri,
      GENERICHTTPVERB.POST,
      payload,
    );
    this.logger.log('generic post api call completed', uri);
    return result;
  }

  private async genericPatch(uri: string, payload: any) {
    this.logger.log('generic post api call started', uri);
    const result = await this.bigIQService.generic(
      uri,
      GENERICHTTPVERB.PATCH,
      payload,
    );
    this.logger.log('generic post api call completed', uri);
    return result;
  }

  private async genericDelete(uri: string) {
    this.logger.log('generic delete api call started', uri);
    const result = await this.bigIQService.generic(uri, GENERICHTTPVERB.DELETE);
    this.logger.log('generic delete api call completed', uri);
    return result;
  }
}
