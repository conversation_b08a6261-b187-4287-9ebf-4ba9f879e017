import { Injectable, Inject } from '@nestjs/common';
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { withResponseErrorHandler } from '../utils/helpers';
import * as FormData from 'form-data';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../loggers/logger.service';
import { LoginPayload } from './dto/load-balancer-services.dto';
import { ENVIRONMENT_VARS, STEPNAMEURL } from '../utils/constants';

@Injectable()
export class BigIQWrapperService {
  constructor(
    private readonly logger: LoggerService,
    @Inject('AXIOS_INSTANCE') private readonly bigIQService: AxiosInstance,
    private readonly configService: ConfigService,
  ) {}

  async createToken(): Promise<string> {
    const url: string = STEPNAMEURL.AUTH;
    const headers = {
      'Content-Type': 'text/plain',
    };

    const username: string = this.configService.get(
      ENVIRONMENT_VARS.BIG_IQ_USERNAME,
    );
    const password: string = this.configService.get(
      ENVIRONMENT_VARS.BIG_IQ_PASSWORD,
    );
    const loginProviderName: string = this.configService.get(
      ENVIRONMENT_VARS.BIG_IQ_LOGIN_PROVIDER_NAME,
    );

    const payload: LoginPayload = {
      username,
      password,
      loginProviderName,
    };

    try {
      this.logger.log('create token wrapper is running');
      const response = await withResponseErrorHandler(
        this.bigIQService.post(url, payload, { headers }),
      );
      this.logger.log('token generated');
      return response.token.token;
    } catch (error) {
      this.logger.error(error, 'error in gettoken wrapper');
    }
  }
  async generic(
    url: string,
    method: any,
    data?: any,
    file?: Express.Multer.File,
    responseType?: any,
  ) {
    let token = await this.createToken();
    let headers = {
      'Content-Type': 'application/json',
      'X-F5-Auth-Token': token,
    };
    if (!!file) {
      const formData = new FormData();
      formData.append('file', file.buffer, { filename: file.originalname });
      data = formData;
      headers = {
        'Content-Type': `multipart/form-data; boundary=${formData.getBoundary()}`,
        'X-F5-Auth-Token': token,
      };
    }
    const config: AxiosRequestConfig = {
      url,
      method,
      headers,
      data,
      responseType,
    };
    this.logger.log('axios config', JSON.stringify(config));
    return await withResponseErrorHandler(this.bigIQService(config));
  }
}
