import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { Logger } from 'nestjs-pino';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  const globalRoutePrefix = configService.get<string>('GLOBAL_ROUTE_PREFIX');
  app.setGlobalPrefix(globalRoutePrefix);
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });
  app.enableCors();
  app.useLogger(app.get(Logger));

  const swaggerConfig = new DocumentBuilder()
    .setTitle('Nebula Load Balancer Service- WORK IN PROGRESS ⚠')
    .setDescription('APIs for Nebula Load Balancer Service')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup(globalRoutePrefix + '/api-docs', app, document);

  await app.listen(configService.get<string>('PORT'));
}
bootstrap();
