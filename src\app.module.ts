import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as Joi from 'joi';
import { APP_FILTER } from '@nestjs/core';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AllExceptionsFilter } from './utils/filters/allException.filter';
import { ENVIRONMENT_VARS } from './utils/constants';
import { LoggerMiddleware } from './loggers/logger.middleware';
import { RequestIdMiddleware } from './filters/requestId.middleware';
import { LoggerModule } from './loggers/logger.module';
import { LoadBalancerModule } from './load-balancer/load-balancer-module';

@Module({
  imports: [
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true,
      validationSchema: Joi.object({
        PORT: Joi.string().required(),
        GLOBAL_ROUTE_PREFIX: Joi.string().required(),
        BIG_IQ_BASE_URL: Joi.string().required(),
        BIG_IQ_USERNAME: Joi.string().required(),
        BIG_IQ_PASSWORD: Joi.string().required(),
        BIG_IQ_CN: Joi.string().required(),
        BIG_IP_MGMT_IP: Joi.string().required(),
      }),
    }),
    LoggerModule,
    LoadBalancerModule,
  ],
  controllers: [AppController],
  providers: [
    { provide: APP_FILTER, useClass: AllExceptionsFilter },
    AppService,
  ],
  exports: [],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes('*');
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
