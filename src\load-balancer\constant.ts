export const Mocked_MetaData_Config = {
  "items": [
    {
      "kind": "cm:adc-core:external-ca:config:externalcaconfigstate",
      "name": "venafi",
      "uuid": "b3be9c56-c1b8-3421-a45e-d9f16c4b6443",
      "selfLink": "https://localhost/mgmt/cm/adc-core/external-ca/config/b3be9c56-c1b8-3421-a45e-d9f16c4b6443",
      "userName": "admin",
      "caProvider": "Venafi",
      "generation": 2,
      "webSdkEndPoint": "vip2.opiehome.com",
      "isTokenBasedAuth": false,
      "lastUpdateMicros": 1732227855198421,
      "encryptedPassword": "q6TPGzUiBbNBz6XBl6Am4rGeLZEXP2tGSm1LpUf1nRM=",
      "autoDeployThreshold": "00:00",
      "autoRenewalThreshold": 7
    }
  ],
  "generation": 4,
  "kind": "cm:adc-core:external-ca:config:https://clouddocs.f5.com/products/big-iq/mgmt-api/v0.0/ApiReferences/bigiq_public_api_ref/r_vnf_policy.html",
  "lastUpdateMicros": 1732227855211026,
  "selfLink": "https://localhost/mgmt/cm/adc-core/external-ca/config"
}

export const Get_Policy_Folders = {
  "policyFolderPath": "\\VED\\Policy\\certificates\\Big IQ",
  "externalCaConfigReference": {
    "link": "https://localhost/mgmt/cm/adc-core/external-ca/config/7d9bf2c4-80b8-3aa0-b868-d34a8ad9b39d"
  },
  "policyFolders": [{
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{9f5660dc-787d-49b6-80a0-d55a1c0c4197}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR High Security(Locked)",
    "guid": "{9f5660dc-787d-49b6-80a0-d55a1c0c4197}",
    "id": "3224",
    "name": "Venafi Generated CSR High Security(Locked)",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885149112584",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{45df3468-c577-4357-8441-019d1e0482b1}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR Medium Security",
    "guid": "{45df3468-c577-4357-8441-019d1e0482b1}",
    "id": "3220",
    "name": "Venafi Generated CSR Medium Security",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885149201716",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{da8deecb-5f8f-4c8e-a3e8-201310e36305}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR High Security",
    "guid": "{da8deecb-5f8f-4c8e-a3e8-201310e36305}",
    "id": "3226",
    "name": "Venafi Generated CSR High Security",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885150411984",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{b816e23e-8b32-486d-b339-488f737595ed}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR Low Security",
    "guid": "{b816e23e-8b32-486d-b339-488f737595ed}",
    "id": "3227",
    "name": "Venafi Generated CSR Low Security",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885149112379",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{7b0dceaf-99fb-4e12-b08b-9f21405e9ea8}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\BigIQ Generated CSR",
    "guid": "{7b0dceaf-99fb-4e12-b08b-9f21405e9ea8}",
    "id": "3214",
    "name": "BigIQ Generated CSR",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885150527180",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{7cdb4598-dcbc-4ad1-b52a-55b42f2fd71e}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR Medium Security(Locked)",
    "guid": "{7cdb4598-dcbc-4ad1-b52a-55b42f2fd71e}",
    "id": "3222",
    "name": "Venafi Generated CSR Medium Security(Locked)",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885150302111",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{c29d0516-6efd-4d5a-8e6a-6fb5f8b718f6}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR Low Security(Locked)",
    "guid": "{c29d0516-6efd-4d5a-8e6a-6fb5f8b718f6}",
    "id": "3225",
    "name": "Venafi Generated CSR Low Security(Locked)",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885150302180",
    "typeName": "Policy"
  },
  {
    "absoluteGUID": "{798ee447-74f4-4c8a-8972-62aff3b2fee3}{266d63f4-0bfc-468a-b41f-d8fa477bd1c0}{5020eaa0-1de7-4be4-bfe8-fbcb7e948502}{2c455b33-7c53-4db1-acdf-62e7f226c469}{3c0a0cfd-f555-4d3a-abae-f4a5b8e86b86}",
    "dn": "\\VED\\Policy\\Certificates\\Big IQ\\Venafi Generated CSR",
    "guid": "{3c0a0cfd-f555-4d3a-abae-f4a5b8e86b86}",
    "id": "3216",
    "name": "Venafi Generated CSR",
    "parent": "\\VED\\Policy\\Certificates\\Big IQ",
    "revision": "636747885150412456",
    "typeName": "Policy"
  }
  ],
  "generation": 0,
  "lastUpdateMicros": 0,
  "kind": "cm:adc-core:external-ca:venafi:policy-folder:venafipolicyfolderstate",
  "selfLink": "https://localhost/mgmt/cm/adc-core/external-ca/venafi/policy-folder"
}

export const DEPLOY_VIPS = {
  SCHEMA: 'https://raw.githubusercontent.com/F5Networks/f5-appsvcs-extension/master/schema/latest/as3-schema.json',
  APPLICATION_DESC: 'test',
  APPLICATION_NAME: 'Nebula Automation Demo',
  APP_SVS_CLASS: 'AS3',
  DECLARATION_CLASS: 'ADC',
  DECLARATION_LABEL: 'CHTR',
  DECLARATION_REMARK: 'Nebula App deployment',
  TARGET_ADDR: '***********',
  SCHEMA_VERSION: '3.44.0',
  DECLARATION_ID: 'id'
}

export const ADD_CERT = {
  NAME: 'pinningPolicyBulkAdd'
}