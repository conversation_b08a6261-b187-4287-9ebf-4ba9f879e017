import { Controller, Post, Body, Get, Query, Param } from '@nestjs/common';
import { LoadBalancerService } from './load-balancer-services';
import {
  MetaDataPolicyFolderDto,
  MetaDataCaConfigResponseDto,
  MetaDataPolicyFolderResponseDto,
} from './dto/meta-data-config.dto';
import { LoggerService } from 'src/loggers/logger.service';
import {
  ClusterDetailsResponseDto,
  ClusterResponseDto,
} from './dto/cluster-details.dto';
import { GenericAPIDto } from './dto/generic.api.dto';
@Controller('')
export class LoadBalancerController {
  constructor(
    private readonly LoadBalancerService: LoadBalancerService,
    private readonly logger: LoggerService,
  ) {}

  @Get('metadata/caConfig')
  async getMetaDataCaConfig(): Promise<MetaDataCaConfigResponseDto> {
    this.logger.log('get meta data configuration');
    return await this.LoadBalancerService.getMetaDataCaConfig();
  }

  @Post('metadata/policy-folders')
  async getPolicyFolders(
    @Body() policyFolderRequestDetails: MetaDataPolicyFolderDto,
  ): Promise<MetaDataPolicyFolderResponseDto> {
    this.logger.log('get policy folders');
    return await this.LoadBalancerService.getPolicyFolders(
      policyFolderRequestDetails,
    );
  }

  @Get('retrieve/cluster-details')
  async getClusterDetails(): Promise<ClusterResponseDto> {
    this.logger.log(
      'Call To Retrieve Target BIG-IP Machine ID, Cluster Name Copy',
    );
    return await this.LoadBalancerService.getClusterDetails();
  }

  @Get('retrieve/peer-device-details')
  async getPeerDeviceDetails(@Body() data): Promise<ClusterResponseDto> {
    this.logger.log('Retrieve Peer Device Machine IDs, if present Copy');
    return await this.LoadBalancerService.getPeerDeviceDetails(data);
  }

  @Get('retrieve/pinning-device-details')
  async getPinningDeviceDetails(@Body() data): Promise<ClusterResponseDto> {
    this.logger.log(' Retrieve Pinning Device Reference Copy');
    return await this.LoadBalancerService.getPinningDeviceDetails(data);
  }

  @Get('retrieve/ssl-certificate-reference')
  async getSslCertificate() {
    this.logger.log(' Retrieve SSL Certificate Reference Copy');
    return await this.LoadBalancerService.getSslCertificate();
  }

  @Get('retrieve/ssl-certificate-key')
  async getSslCertificateKey() {
    this.logger.log(' Retrieve SSL Key Reference Copy');
    return await this.LoadBalancerService.getSslCertificateKey();
  }

  @Post('add/certificate-details-to-pinning-policies')
  async addCertificateDetails(@Body() data: any) {
    this.logger.log(' Add Cert and Key to Pinning Policies Copy');
    return await this.LoadBalancerService.addCertificateDetails(data);
  }

  @Post('deploy/certificate-details-to-pinning-policies')
  async deployCertificateDetails(@Body() data: any) {
    this.logger.log(' Deploy Pinning Policies as Partial Changes Copy');
    return await this.LoadBalancerService.deployCertificateDetails(data);
  }

  @Get('check/deployment-status')
  async checkDeploymentStatus(@Body() data: any) {
    this.logger.log(' Check Deployment Status Copy');
    const deploymentTaskId = data.deploymentTaskId;
    return await this.LoadBalancerService.checkDeploymentStatus(
      deploymentTaskId,
    );
  }

  @Get('retrieve/as3-declaration')
  async getAs3Declaration() {
    this.logger.log(' GET AS3 Declaration');
    return await this.LoadBalancerService.getAs3Declaration();
  }

  @Post('deploy/vips')
  async deployVips(@Body() data: any) {
    this.logger.log(' Deploy VIPs');
    return await this.LoadBalancerService.deployVips(data);
  }

  @Get('check/as3-status')
  async checkAs3Status(@Body() data: any) {
    this.logger.log(' GET AS3 Status');
    const deploymentTaskId = data.id;
    return await this.LoadBalancerService.checkAs3Status(deploymentTaskId);
  }
  @Post('generic')
  async generic(@Body() data: GenericAPIDto) {
    this.logger.log('Generic API call received', data);
    return await this.LoadBalancerService.generic(data);
  }
}
