// src/logger/logger.service.ts
import { Injectable, Lo<PERSON>, Scope } from '@nestjs/common';
import * as winston from 'winston';

@Injectable()
export class LoggerService extends Logger {
  protected context: string;

  constructor() {
    super();
    this.context = 'Load-Balancer-Service';
  }

  log(message: string, ...optionalParams: any[]) {
    /*
      Example =>
    
      Converts -
      this.logger.log("Log text", "sample string", {obj: "Sample value"});

      to -
      this.logger.log("Log text %o %o", "sample string", {obj: "Sample value"});

    */

    super.log(
      `${message}${optionalParams?.length ? ' %o'.repeat(optionalParams.length) : ''}`,
      ...optionalParams,
    );
  }

  debug(message: string, ...optionalParams: any[]) {
    super.debug(
      `${message}${optionalParams?.length ? ' %o'.repeat(optionalParams.length) : ''}`,
      ...optionalParams,
    );
  }

  warn(message: string, ...optionalParams: any[]) {
    super.warn(
      `${message}${optionalParams?.length ? ' %o'.repeat(optionalParams.length) : ''}`,
      ...optionalParams,
    );
  }

  verbose(message: string, ...optionalParams: any[]) {
    super.verbose(
      `${message}${optionalParams?.length ? ' %o'.repeat(optionalParams.length) : ''}`,
      ...optionalParams,
    );
  }

  error(e: any, ...optionalParams: any[]) {
    super.error(
      e,
      `${optionalParams?.length ? ' %o'.repeat(optionalParams.length) : ''}`,
      ...optionalParams,
    );
  }
}
